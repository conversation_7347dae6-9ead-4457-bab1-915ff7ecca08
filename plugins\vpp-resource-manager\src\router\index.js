/** @type {import("vue-router").RouteConfig[]} */

import main from "./main.vue";

export const appRoutes = [
  {
    path: "/",
    component: main,
    children: [
      {
        path: "",
        redirect: "/list"
      },
      {
        meta: {
          keepAlive: true
        },
        path: "/grid",
        component: () => import("@/projects/grid/index.vue")
      },
      {
        path: "/noviceGuidePage1",
        component: () => import("@/projects/noviceGuide/page1/index.vue")
      },
      {
        path: "/noviceGuidePage2",
        component: () => import("@/projects/noviceGuide/page2/index.vue")
      },
      {
        path: "/list",
        component: () => import("@/projects/list/index.vue")
      },
    ]
  }
];

export default {
  // mode: "history",
  routes: appRoutes
};
