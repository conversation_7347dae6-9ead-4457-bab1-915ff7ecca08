

### 📦 环境要求

- Node.js: 18.20.4
- Git: 支持 submodule

# 🚀 快速开始

## 安装所有工作区依赖
```bash
pnpm install
```

## 启动主应用
```bash
pnpm dev:main
```

## 启动子应用插件
```bash
pnpm dev:vpp
```


## 🔧 开发指南

#### 在主应用中查看独立运行的项目
1. 修改 `Fusion-template\main\src\omega\fusion.js` 文件
```js
//开发环境的配置
const develop = {
  appList: [
    {
      name: "vpp-agg",
      // url: "/fusion/vpp-agg-1.0.0/",
      url: "//localhost:9530"
      // alive: false
      // preload: true,
      // exec: true
    }
  ],
  options: {
    systemConfig: "local",
    isMultiProject: true
  }
};
```
2. 运行 `pnpm dev:main` 启动主应用
3. 在浏览器中访问 `http://localhost:9528`
4. 


## 📦 构建与部署

### 构建命令

```bash
# 构建主应用
pnpm --filter main build

# 构建子应用
pnpm --filter sub build

# 构建并生成分析报告
pnpm --filter main build:report
pnpm --filter sub build:report

# DevOps 构建（主应用）
pnpm --filter main build:devops
```
